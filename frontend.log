
> exchange-mall@0.0.1 dev
> vite


  VITE v4.5.9  ready in 207 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

[1m[33m[@vue/compiler-sfc][0m[33m `defineProps` is a compiler macro and no longer needs to be imported.[0m

[1m[33m[@vue/compiler-sfc][0m[33m `defineEmits` is a compiler macro and no longer needs to be imported.[0m

files in the public directory are served at the root path.
Instead of /public/images/payment/pay.png, use /images/payment/pay.png.
files in the public directory are served at the root path.
Instead of /public/images/payment/pay.png, use /images/payment/pay.png.
files in the public directory are served at the root path.
Instead of /public/images/payment/pay.png, use /images/payment/pay.png.
