====== 服务器启动环境变量 ======
NODE_ENV: development
是否本地开发环境: 是
SERVER_URL (自动检测后): http://localhost:3000
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
====== 环境变量输出结束 ======
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码图片目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
图片上传目录权限正常
静态文件路径1: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
静态文件路径2: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
上传目录绝对路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
=== 飞书配置加载 ===
NODE_ENV: development
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
process.env: {
  NODE_ENV: 'development',
  PORT: '3000',
  FEISHU_APP_ID: undefined,
  FEISHU_APP_SECRET: undefined,
  FEISHU_REDIRECT_URI: 'http://localhost:3000/api/feishu/callback'
}
计算得到的redirectUri: http://localhost:3000/api/feishu/callback
==================================
服务器启动成功，端口: 3000
API地址: http://localhost:3000/api
上传API: http://localhost:3000/api/upload
静态文件: http://localhost:3000/uploads
==================================
初始化定时报告服务...
✅ 定时报告服务初始化完成
📊 定时统计报告服务已启动
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT 1+1 AS result Elapsed time: 1ms
Database connection has been established successfully.
[0mGET /api/health [32m200[0m 2.787 ms - 49[0m
[0mGET /api/env-check [32m200[0m 0.840 ms - 151[0m
开始执行统计查询...
日期范围: 2025-05-06T10:11:11.768Z 至 2025-06-05T10:11:11.768Z
Executed (default): SELECT DATE(`createdAt`) AS `date`, count(`id`) AS `count` FROM `logs` AS `Log` WHERE `Log`.`createdAt` BETWEEN '2025-05-06 18:11:11' AND '2025-06-05 18:11:11' GROUP BY DATE(`createdAt`) ORDER BY DATE(`createdAt`) ASC; Elapsed time: 1ms
日期统计查询成功: 4
Executed (default): SELECT `action`, count(`id`) AS `count` FROM `logs` AS `Log` GROUP BY `action` ORDER BY count(`id`) DESC; Elapsed time: 1ms
操作类型统计查询成功: 12
Executed (default): SELECT `entityType`, count(`id`) AS `count` FROM `logs` AS `Log` GROUP BY `entityType` ORDER BY count(`id`) DESC; Elapsed time: 1ms
实体类型统计查询成功: 5
Executed (default): SELECT `username`, count(`id`) AS `loginCount`, MAX(`createdAt`) AS `lastLoginTime` FROM `logs` AS `Log` WHERE `Log`.`action` = 'user_login' AND `Log`.`createdAt` BETWEEN '2025-05-06 18:11:11' AND '2025-06-05 18:11:11' GROUP BY `username` ORDER BY count(`id`) DESC; Elapsed time: 1ms
用户登录统计查询成功: 4
Executed (default): SELECT `action`, count(`id`) AS `count` FROM `logs` AS `Log` WHERE `Log`.`entityType` = 'product' AND `Log`.`createdAt` BETWEEN '2025-05-06 18:11:11' AND '2025-06-05 18:11:11' GROUP BY `action` ORDER BY count(`id`) DESC; Elapsed time: 2ms
商品操作统计查询成功: 2
[0mGET /api/logs/stats [32m200[0m 16.564 ms - 1264[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
用户认证信息(已更新): {
  id: 59,
  username: '秦昭',
  role: 'admin',
  isAdmin: true,
  tokenRole: 'admin',
  tokenIsAdmin: true
}
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
[0mGET /api/auth/profile [32m200[0m 10.583 ms - 522[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png',
  uploadTime: '2025-06-05T10:04:39.338Z'
}
检查收款码文件 qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png 是否存在: true
收款码文件大小: 294304 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png
[0mGET /api/system/payment-qrcode [32m200[0m 2.694 ms - 202[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 18ms
认证成功, 用户: 秦昭 角色: admin
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 31ms
认证成功, 用户: 秦昭 角色: admin
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 10ms
[0mGET /api/notifications/unread-count [32m200[0m 62.639 ms - 35[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 8ms
认证成功, 用户: 秦昭 角色: admin
🔧 管理员获取通知配置列表...
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT count(*) AS `count` FROM `workplaces` AS `Workplace`; Elapsed time: 9ms
Executed (default): SELECT `id`, `name`, `code`, `description`, `isActive`, `createdAt`, `updatedAt` FROM `workplaces` AS `Workplace` ORDER BY `Workplace`.`name` ASC LIMIT 0, 10; Elapsed time: 2ms
[0mGET /api/system/workplaces?page=1&limit=10 [32m200[0m 64.629 ms - 845[0m
Executed (default): SELECT `notification_type` AS `notificationType` FROM `notification_configs` AS `NotificationConfig`; Elapsed time: 3ms
Executed (default): SELECT `id`, `notification_type` AS `notificationType`, `enabled`, `webhook_url` AS `webhookUrl`, `schedule_time` AS `scheduleTime`, `retry_count` AS `retryCount`, `template_id` AS `templateId`, `schedule_id` AS `scheduleId`, `advanced_settings` AS `advancedSettings`, `created_at`, `updated_at`, `template_id`, `schedule_id` FROM `notification_configs` AS `NotificationConfig` ORDER BY `notificationType` ASC; Elapsed time: 1ms
[0mGET /uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png?t=1749118273906 [32m200[0m 27.450 ms - 294304[0m
[0mGET /api/system/notification-configs [32m200[0m 40.355 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 0ms
认证成功, 用户: 秦昭 角色: admin
🔍 检查Webhook配置状态...
Executed (default): SELECT `notification_type` AS `notificationType` FROM `notification_configs` AS `NotificationConfig`; Elapsed time: 0ms
Executed (default): SELECT `id`, `notification_type` AS `notificationType`, `enabled`, `webhook_url` AS `webhookUrl`, `schedule_time` AS `scheduleTime`, `retry_count` AS `retryCount`, `template_id` AS `templateId`, `schedule_id` AS `scheduleId`, `advanced_settings` AS `advancedSettings`, `created_at`, `updated_at`, `template_id`, `schedule_id` FROM `notification_configs` AS `NotificationConfig` ORDER BY `notificationType` ASC; Elapsed time: 1ms
🔍 Webhook状态检查结果: { hasWebhookUrl: false, hasConfigWithWebhook: true, configCount: 11 }
[0mGET /api/system/webhook-status [32m200[0m 3.821 ms - 130[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
🔧 管理员获取通知统计数据...
Executed (default): SELECT `status`, COUNT('*') AS `count` FROM `notification_logs` AS `NotificationLog` GROUP BY `status`; Elapsed time: 1ms
Executed (default): SELECT `notification_type` AS `notificationType`, `status`, COUNT('*') AS `count`, AVG(`response_time`) AS `avgResponseTime` FROM `notification_logs` AS `NotificationLog` GROUP BY `notificationType`, `status`; Elapsed time: 1ms
Executed (default): SELECT DATE(`created_at`) AS `date`, `status`, COUNT('*') AS `count` FROM `notification_logs` AS `NotificationLog` WHERE (`NotificationLog`.`created_at` >= '2025-05-29 18:11:14' AND `NotificationLog`.`created_at` <= '2025-06-05 18:11:14') GROUP BY DATE(`created_at`), `status` ORDER BY DATE(`created_at`) ASC; Elapsed time: 5ms
Executed (default): SELECT COUNT('*') AS `totalRetries`, AVG(`retry_count`) AS `avgRetries`, MAX(`retry_count`) AS `maxRetries` FROM `notification_logs` AS `NotificationLog` WHERE `NotificationLog`.`retry_count` > 0; Elapsed time: 1ms
[0mGET /api/system/notification-stats [32m200[0m 12.715 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png',
  uploadTime: '2025-06-05T10:04:39.338Z'
}
检查收款码文件 qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png 是否存在: true
收款码文件大小: 294304 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png
[0mGET /api/system/payment-qrcode [36m304[0m 1.805 ms - -[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 5ms
认证成功, 用户: 秦昭 角色: admin
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT count(*) AS `count` FROM `workplaces` AS `Workplace`; Elapsed time: 10ms
Executed (default): SELECT `id`, `name`, `code`, `description`, `isActive`, `createdAt`, `updatedAt` FROM `workplaces` AS `Workplace` ORDER BY `Workplace`.`name` ASC LIMIT 0, 10; Elapsed time: 10ms
[0mGET /api/system/workplaces?page=1&limit=10 [36m304[0m 23.260 ms - -[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 7ms
认证成功, 用户: 秦昭 角色: admin
🔧 管理员获取通知配置列表...
Executed (default): SELECT `notification_type` AS `notificationType` FROM `notification_configs` AS `NotificationConfig`; Elapsed time: 2ms
Executed (default): SELECT `id`, `notification_type` AS `notificationType`, `enabled`, `webhook_url` AS `webhookUrl`, `schedule_time` AS `scheduleTime`, `retry_count` AS `retryCount`, `template_id` AS `templateId`, `schedule_id` AS `scheduleId`, `advanced_settings` AS `advancedSettings`, `created_at`, `updated_at`, `template_id`, `schedule_id` FROM `notification_configs` AS `NotificationConfig` ORDER BY `notificationType` ASC; Elapsed time: 1ms
[0mGET /api/system/notification-configs [36m304[0m 20.543 ms - -[0m
[0mGET /uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png?t=1749118279598 [32m200[0m 2.507 ms - 294304[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 0ms
认证成功, 用户: 秦昭 角色: admin
🔍 检查Webhook配置状态...
Executed (default): SELECT `notification_type` AS `notificationType` FROM `notification_configs` AS `NotificationConfig`; Elapsed time: 0ms
Executed (default): SELECT `id`, `notification_type` AS `notificationType`, `enabled`, `webhook_url` AS `webhookUrl`, `schedule_time` AS `scheduleTime`, `retry_count` AS `retryCount`, `template_id` AS `templateId`, `schedule_id` AS `scheduleId`, `advanced_settings` AS `advancedSettings`, `created_at`, `updated_at`, `template_id`, `schedule_id` FROM `notification_configs` AS `NotificationConfig` ORDER BY `notificationType` ASC; Elapsed time: 1ms
🔍 Webhook状态检查结果: { hasWebhookUrl: false, hasConfigWithWebhook: true, configCount: 11 }
[0mGET /api/system/webhook-status [36m304[0m 3.949 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
🔧 管理员获取通知统计数据...
Executed (default): SELECT `status`, COUNT('*') AS `count` FROM `notification_logs` AS `NotificationLog` GROUP BY `status`; Elapsed time: 1ms
Executed (default): SELECT `notification_type` AS `notificationType`, `status`, COUNT('*') AS `count`, AVG(`response_time`) AS `avgResponseTime` FROM `notification_logs` AS `NotificationLog` GROUP BY `notificationType`, `status`; Elapsed time: 1ms
Executed (default): SELECT DATE(`created_at`) AS `date`, `status`, COUNT('*') AS `count` FROM `notification_logs` AS `NotificationLog` WHERE (`NotificationLog`.`created_at` >= '2025-05-29 18:11:19' AND `NotificationLog`.`created_at` <= '2025-06-05 18:11:19') GROUP BY DATE(`created_at`), `status` ORDER BY DATE(`created_at`) ASC; Elapsed time: 4ms
Executed (default): SELECT COUNT('*') AS `totalRetries`, AVG(`retry_count`) AS `avgRetries`, MAX(`retry_count`) AS `maxRetries` FROM `notification_logs` AS `NotificationLog` WHERE `NotificationLog`.`retry_count` > 0; Elapsed time: 0ms
[0mGET /api/system/notification-stats [36m304[0m 13.590 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.643 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 220 AND `Notification`.`isRead` = false; Elapsed time: 2ms
[0mGET /api/notifications/unread-count [36m304[0m 8.951 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 34ms
认证成功, 用户: 秦昭 角色: admin
尝试删除旧的收款码文件...
读取到的收款码配置: {
  filename: 'qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png',
  uploadTime: '2025-06-05T10:04:39.338Z'
}
删除旧文件: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment/qrcode_9d7056eb-4872-4c8a-a666-c4a4408b717e.png
Executed (default): INSERT INTO `logs` (`id`,`action`,`entityType`,`entityId`,`userId`,`username`,`ipAddress`,`deviceInfo`,`description`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?); Elapsed time: 9ms
收款码操作日志已记录: delete_qrcode
[0mDELETE /api/system/payment-qrcode [32m200[0m 68.423 ms - 53[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 3ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 13.121 ms - -[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 5ms
用户认证信息(已更新): {
  id: 220,
  username: '测试',
  role: 'user',
  isAdmin: false,
  tokenRole: 'user',
  tokenIsAdmin: false
}
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 2ms
[0mGET /api/auth/profile [32m200[0m 12.391 ms - 526[0m
Executed (default): SELECT `id`, `name`, `description`, `sortOrder`, `createdAt`, `updatedAt` FROM `categories` AS `Category` ORDER BY `Category`.`sortOrder` ASC, `Category`.`name` ASC; Elapsed time: 3ms
[0mGET /api/categories [36m304[0m 3.400 ms - -[0m
Executed (default): SELECT count(DISTINCT(`Product`.`id`)) AS `count` FROM `products` AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` WHERE `Product`.`status` = 'active'; Elapsed time: 1ms
Executed (default): SELECT `Product`.*, `Category`.`id` AS `Category.id`, `Category`.`name` AS `Category.name`, `ProductImages`.`id` AS `ProductImages.id`, `ProductImages`.`imageUrl` AS `ProductImages.imageUrl`, `ProductImages`.`sortOrder` AS `ProductImages.sortOrder` FROM (SELECT `Product`.`id`, `Product`.`name`, `Product`.`categoryId`, `Product`.`lyPrice`, `Product`.`rmbPrice`, `Product`.`description`, `Product`.`stock`, `Product`.`exchangeCount`, `Product`.`isHot`, `Product`.`isNew`, `Product`.`status`, `Product`.`createdAt`, `Product`.`updatedAt` FROM `products` AS `Product` WHERE `Product`.`status` = 'active' ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC LIMIT 0, 50) AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC; Elapsed time: 3ms
[0mGET /api/products?page=1&limit=50&showAll=false [36m304[0m 12.449 ms - -[0m
Executed (default): SELECT count(`Announcement`.`id`) AS `count` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active'; Elapsed time: 5ms
Executed (default): SELECT `Announcement`.`id`, `Announcement`.`title`, `Announcement`.`content`, `Announcement`.`contentHtml`, `Announcement`.`type`, `Announcement`.`status`, `Announcement`.`imageUrl`, `Announcement`.`imageUrls`, `Announcement`.`createdBy`, `Announcement`.`createdAt`, `Announcement`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`username` AS `creator.username` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active' ORDER BY `Announcement`.`createdAt` DESC LIMIT 0, 1; Elapsed time: 6ms
[0mGET /api/announcements?page=1&limit=1&sort=newest&status=active [36m304[0m 7.515 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 2ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT MIN(`lyPrice`) AS `minLyPrice`, MAX(`lyPrice`) AS `maxLyPrice`, MIN(`rmbPrice`) AS `minRmbPrice`, MAX(`rmbPrice`) AS `maxRmbPrice` FROM `products` AS `Product` WHERE `Product`.`status` = 'active'; Elapsed time: 1ms
[0mGET /api/products/price-ranges [36m304[0m 1.893 ms - -[0m
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 220 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.522 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 2ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT `Notification`.`id`, `Notification`.`type`, `Notification`.`sourceId`, `Notification`.`title`, `Notification`.`content`, `Notification`.`isRead`, `Notification`.`recipientId`, `Notification`.`createdAt`, `Notification`.`updatedAt`, `User`.`id` AS `User.id`, `User`.`username` AS `User.username` FROM `notifications` AS `Notification` LEFT OUTER JOIN `users` AS `User` ON `Notification`.`recipientId` = `User`.`id` WHERE `Notification`.`recipientId` = 220 ORDER BY `Notification`.`createdAt` DESC; Elapsed time: 8ms
[0mGET /api/notifications [36m304[0m 15.792 ms - -[0m
[0mGET /uploads/d1ab861f34573e71ca3e03b0d651855a.png [36m304[0m 8.403 ms - -[0m
[0mGET /uploads/b93e96a0d72d9797f7048afa278a14ef.png [36m304[0m 1.421 ms - -[0m
[0mGET /uploads/2746d2a5eb1720ae0979ef7af743a6ba.png [36m304[0m 1.606 ms - -[0m
[0mGET /uploads/1c74fe6d6ba7e3df39eb2704124ce80e.png [36m304[0m 1.885 ms - -[0m
[0mGET /uploads/9445de36394232e301b613a2ed137a10.png [36m304[0m 0.620 ms - -[0m
[0mGET /uploads/989a1fbee017709b9b7753e41e6433ac.png [36m304[0m 1.525 ms - -[0m
[0mGET /uploads/9fa827b983e8027252999d840925e6d0.png [36m304[0m 1.496 ms - -[0m
[0mGET /uploads/f99ae6bb16e5771c6366196a4f7b83e0.png [36m304[0m 1.310 ms - -[0m
[0mGET /uploads/images/045c23882b1e4c71f834e99086b3087f.png [36m304[0m 1.766 ms - -[0m
[0mGET /uploads/images/63ff1f35b9f109bcb0248b167adb9d65.jpeg [36m304[0m 0.894 ms - -[0m
[0mGET /uploads/34de1888c250bfaa6135298e78e0c45d.png [36m304[0m 0.887 ms - -[0m
[0mGET /uploads/c6f6521cfd30b8551ac7c45c730859e8.png [36m304[0m 1.008 ms - -[0m
[0mGET /uploads/8c3c43fcd78445e53fe7a670de4fc544.png [36m304[0m 0.792 ms - -[0m
[0mGET /uploads/a3d2662d0d4e6b4730b4b923093f64be.png [36m304[0m 0.767 ms - -[0m
[0mGET /uploads/f7b621699109d019b255f73f757e187f.png [36m304[0m 0.415 ms - -[0m
Executed (default): SELECT `id`, `name`, `categoryId`, `lyPrice`, `rmbPrice`, `description`, `stock`, `exchangeCount`, `isHot`, `isNew`, `status`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`id` = '14'; Elapsed time: 2ms
Executed (default): SELECT `id`, `productId`, `imageUrl`, `sortOrder`, `createdAt`, `updatedAt` FROM `product_images` AS `ProductImage` WHERE `ProductImage`.`productId` = '14' ORDER BY `ProductImage`.`sortOrder` ASC; Elapsed time: 1ms
[0mGET /api/product-images/product/14 [32m200[0m 6.860 ms - 198[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 3ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT `id`, `name`, `code` FROM `workplaces` AS `Workplace` WHERE `Workplace`.`isActive` = true ORDER BY `Workplace`.`name` ASC; Elapsed time: 11ms
[0mGET /api/system/workplaces/active [36m304[0m 20.042 ms - -[0m
正在获取支付收款码信息...
读取到的收款码配置: { filename: null, uploadTime: null }
配置中未设置收款码文件名
[0mGET /api/system/payment-qrcode [32m200[0m 1.357 ms - 73[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 7.929 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.353 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 15ms
认证成功, 用户: 秦昭 角色: admin
开始处理支付收款码上传请求...
请求头: {"host":"localhost:3000","connection":"keep-alive","content-length":"81373","sec-ch-ua-platform":"\"macOS\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTksInVzZXJuYW1lIjoi56em5pitIiwicm9sZSI6ImFkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzQ5MTE3ODE5LCJleHAiOjE3NDkxMjE0MTl9.MBvWXDvjfcfz7cChAMYgu7cyFc-XqwLvhsCbF_uldrs","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","content-type":"multipart/form-data; boundary=----WebKitFormBoundaryb3yDXTAW28aPv8v2","sec-ch-ua-mobile":"?0","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"zh-CN,zh;q=0.9"}
支付码目录写入权限正常
收到文件: åçµå¨å¥è£.jpeg 类型: image/jpeg
生成唯一文件名: qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
文件上传成功: {
  fieldname: 'file',
  originalname: 'å\x85\x85ç\x94µå\x99¨å¥\x97è£\x85.jpeg',
  encoding: '7bit',
  mimetype: 'image/jpeg',
  destination: '/Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment',
  filename: 'qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  path: '/Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  size: 81179
}
文件大小: 81179 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
尝试删除旧的收款码文件...
读取到的收款码配置: { filename: null, uploadTime: null }
配置中没有文件名，无需删除旧文件
删除旧收款码结果: false
已更新收款码配置: {
  filename: 'qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  uploadTime: '2025-06-05T10:11:55.180Z'
}
Executed (default): INSERT INTO `logs` (`id`,`action`,`entityType`,`entityId`,`userId`,`username`,`ipAddress`,`deviceInfo`,`description`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?); Elapsed time: 6ms
收款码操作日志已记录: upload_qrcode
[0mPOST /api/system/payment-qrcode [32m200[0m 47.677 ms - 259[0m
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  uploadTime: '2025-06-05T10:11:55.180Z'
}
检查收款码文件 qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg 是否存在: true
收款码文件大小: 81179 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
[0mGET /api/system/payment-qrcode [32m200[0m 1.456 ms - 204[0m
[0mGET /uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg?t=1749118316199 [32m200[0m 1.034 ms - 81179[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 3ms
用户认证信息(已更新): {
  id: 220,
  username: '测试',
  role: 'user',
  isAdmin: false,
  tokenRole: 'user',
  tokenIsAdmin: false
}
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
[0mGET /api/auth/profile [36m304[0m 12.754 ms - -[0m
Executed (default): SELECT `id`, `name`, `description`, `sortOrder`, `createdAt`, `updatedAt` FROM `categories` AS `Category` ORDER BY `Category`.`sortOrder` ASC, `Category`.`name` ASC; Elapsed time: 3ms
[0mGET /api/categories [36m304[0m 5.493 ms - -[0m
Executed (default): SELECT count(DISTINCT(`Product`.`id`)) AS `count` FROM `products` AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` WHERE `Product`.`status` = 'active'; Elapsed time: 4ms
Executed (default): SELECT `Product`.*, `Category`.`id` AS `Category.id`, `Category`.`name` AS `Category.name`, `ProductImages`.`id` AS `ProductImages.id`, `ProductImages`.`imageUrl` AS `ProductImages.imageUrl`, `ProductImages`.`sortOrder` AS `ProductImages.sortOrder` FROM (SELECT `Product`.`id`, `Product`.`name`, `Product`.`categoryId`, `Product`.`lyPrice`, `Product`.`rmbPrice`, `Product`.`description`, `Product`.`stock`, `Product`.`exchangeCount`, `Product`.`isHot`, `Product`.`isNew`, `Product`.`status`, `Product`.`createdAt`, `Product`.`updatedAt` FROM `products` AS `Product` WHERE `Product`.`status` = 'active' ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC LIMIT 0, 50) AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC; Elapsed time: 4ms
[0mGET /api/products?page=1&limit=50&showAll=false [36m304[0m 9.145 ms - -[0m
Executed (default): SELECT count(`Announcement`.`id`) AS `count` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active'; Elapsed time: 4ms
Executed (default): SELECT `Announcement`.`id`, `Announcement`.`title`, `Announcement`.`content`, `Announcement`.`contentHtml`, `Announcement`.`type`, `Announcement`.`status`, `Announcement`.`imageUrl`, `Announcement`.`imageUrls`, `Announcement`.`createdBy`, `Announcement`.`createdAt`, `Announcement`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`username` AS `creator.username` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active' ORDER BY `Announcement`.`createdAt` DESC LIMIT 0, 1; Elapsed time: 3ms
[0mGET /api/announcements?page=1&limit=1&sort=newest&status=active [36m304[0m 5.480 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT MIN(`lyPrice`) AS `minLyPrice`, MAX(`lyPrice`) AS `maxLyPrice`, MIN(`rmbPrice`) AS `minRmbPrice`, MAX(`rmbPrice`) AS `maxRmbPrice` FROM `products` AS `Product` WHERE `Product`.`status` = 'active'; Elapsed time: 3ms
[0mGET /api/products/price-ranges [36m304[0m 3.793 ms - -[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 220 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.751 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT `Notification`.`id`, `Notification`.`type`, `Notification`.`sourceId`, `Notification`.`title`, `Notification`.`content`, `Notification`.`isRead`, `Notification`.`recipientId`, `Notification`.`createdAt`, `Notification`.`updatedAt`, `User`.`id` AS `User.id`, `User`.`username` AS `User.username` FROM `notifications` AS `Notification` LEFT OUTER JOIN `users` AS `User` ON `Notification`.`recipientId` = `User`.`id` WHERE `Notification`.`recipientId` = 220 ORDER BY `Notification`.`createdAt` DESC; Elapsed time: 0ms
[0mGET /api/notifications [36m304[0m 2.512 ms - -[0m
[0mGET /uploads/d1ab861f34573e71ca3e03b0d651855a.png [36m304[0m 0.748 ms - -[0m
[0mGET /uploads/1c74fe6d6ba7e3df39eb2704124ce80e.png [36m304[0m 0.248 ms - -[0m
[0mGET /uploads/b93e96a0d72d9797f7048afa278a14ef.png [36m304[0m 0.291 ms - -[0m
[0mGET /uploads/images/045c23882b1e4c71f834e99086b3087f.png [36m304[0m 1.231 ms - -[0m
[0mGET /uploads/9445de36394232e301b613a2ed137a10.png [36m304[0m 1.264 ms - -[0m
[0mGET /uploads/images/63ff1f35b9f109bcb0248b167adb9d65.jpeg [36m304[0m 1.255 ms - -[0m
[0mGET /uploads/f99ae6bb16e5771c6366196a4f7b83e0.png [36m304[0m 0.596 ms - -[0m
[0mGET /uploads/9fa827b983e8027252999d840925e6d0.png [36m304[0m 0.783 ms - -[0m
[0mGET /uploads/989a1fbee017709b9b7753e41e6433ac.png [36m304[0m 0.197 ms - -[0m
[0mGET /uploads/2746d2a5eb1720ae0979ef7af743a6ba.png [36m304[0m 0.222 ms - -[0m
Executed (default): SELECT `id`, `name`, `categoryId`, `lyPrice`, `rmbPrice`, `description`, `stock`, `exchangeCount`, `isHot`, `isNew`, `status`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`id` = '21'; Elapsed time: 1ms
Executed (default): SELECT `id`, `productId`, `imageUrl`, `sortOrder`, `createdAt`, `updatedAt` FROM `product_images` AS `ProductImage` WHERE `ProductImage`.`productId` = '21' ORDER BY `ProductImage`.`sortOrder` ASC; Elapsed time: 1ms
[0mGET /api/product-images/product/21 [36m304[0m 4.094 ms - -[0m
[0mGET /uploads/bde78ff33c77c535e0666cf6b7a5f82a.png [36m304[0m 0.419 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT `id`, `name`, `code` FROM `workplaces` AS `Workplace` WHERE `Workplace`.`isActive` = true ORDER BY `Workplace`.`name` ASC; Elapsed time: 1ms
[0mGET /api/system/workplaces/active [36m304[0m 7.077 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 2.668 ms - -[0m
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  uploadTime: '2025-06-05T10:11:55.180Z'
}
检查收款码文件 qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg 是否存在: true
收款码文件大小: 81179 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
[0mGET /api/system/payment-qrcode [32m200[0m 2.271 ms - 204[0m
[0mGET /uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg?t=1749118325674 [32m200[0m 0.784 ms - 81179[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT `id`, `name`, `code` FROM `workplaces` AS `Workplace` WHERE `Workplace`.`isActive` = true ORDER BY `Workplace`.`name` ASC; Elapsed time: 1ms
[0mGET /api/system/workplaces/active [36m304[0m 5.313 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.847 ms - -[0m
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  uploadTime: '2025-06-05T10:11:55.180Z'
}
检查收款码文件 qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg 是否存在: true
收款码文件大小: 81179 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
[0mGET /api/system/payment-qrcode [36m304[0m 1.305 ms - -[0m
[0mGET /uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg?t=1749118335327 [32m200[0m 0.769 ms - 81179[0m
Express-file-upload: Temporary file path is /var/folders/77/sbdmjvfx5_b1m27lrcgt4yw00000gn/T/uploads/tmp-1-1749118339799
Express-file-upload: Opening write stream for file->123.png...
Express-file-upload: New upload started file->123.png, bytes:0
Express-file-upload: Uploading file->123.png, bytes:64503...
Express-file-upload: Uploading file->123.png, bytes:130039...
Express-file-upload: Uploading file->123.png, bytes:195575...
Express-file-upload: Uploading file->123.png, bytes:261111...
Express-file-upload: Uploading file->123.png, bytes:326647...
Express-file-upload: Uploading file->123.png, bytes:392183...
Express-file-upload: Uploading file->123.png, bytes:457719...
Express-file-upload: Uploading file->123.png, bytes:523255...
Express-file-upload: Uploading file->123.png, bytes:588791...
Express-file-upload: Uploading file->123.png, bytes:653920...
Express-file-upload: Upload finished file->123.png, bytes:653920
Express-file-upload: Upload file->123.png completed, bytes:653920.
Express-file-upload: Busboy finished parsing request.
收到支付截图上传请求 - /api/uploads
请求头: {"host":"localhost:3000","connection":"keep-alive","content-length":"654100","sec-ch-ua-platform":"\"macOS\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MjIwLCJ1c2VybmFtZSI6Iua1i-ivlSIsInJvbGUiOiJ1c2VyIiwiaXNBZG1pbiI6ZmFsc2UsImlhdCI6MTc0OTExNzc5OCwiZXhwIjoxNzQ5MTIxMzk4fQ.3kvsHaRIT21XXwRe7KbAOIWfBhk7It9xIAvCcSkjgds","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","content-type":"multipart/form-data; boundary=----WebKitFormBoundaryq55mxqmaYSQbfhsB","sec-ch-ua-mobile":"?0","accept":"*/*","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"zh-CN,zh;q=0.9"}
请求体类型: multipart/form-data; boundary=----WebKitFormBoundaryq55mxqmaYSQbfhsB
请求来源: http://localhost:5173
支付截图文件名: 425f0d38-a38e-484f-bf70-1485b5d64ea3.png
支付截图保存路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images/425f0d38-a38e-484f-bf70-1485b5d64ea3.png
Express-file-upload: Moving temporary file /var/folders/77/sbdmjvfx5_b1m27lrcgt4yw00000gn/T/uploads/tmp-1-1749118339799 to /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images/425f0d38-a38e-484f-bf70-1485b5d64ea3.png
支付截图上传成功: http://localhost:3000/uploads/images/425f0d38-a38e-484f-bf70-1485b5d64ea3.png
[0mPOST /api/uploads [32m200[0m 16.781 ms - 401[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 6.230 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 2ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 220 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 7.951 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.208 ms - -[0m
Executed (default): SELECT `id`, `name`, `categoryId`, `lyPrice`, `rmbPrice`, `description`, `stock`, `exchangeCount`, `isHot`, `isNew`, `status`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`id` = '21'; Elapsed time: 5ms
Executed (default): SELECT `id`, `productId`, `imageUrl`, `sortOrder`, `createdAt`, `updatedAt` FROM `product_images` AS `ProductImage` WHERE `ProductImage`.`productId` = '21' ORDER BY `ProductImage`.`sortOrder` ASC; Elapsed time: 1ms
[0mGET /api/product-images/product/21 [36m304[0m 8.627 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.861 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
尝试删除旧的收款码文件...
读取到的收款码配置: {
  filename: 'qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg',
  uploadTime: '2025-06-05T10:11:55.180Z'
}
删除旧文件: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment/qrcode_72c59f38-304f-4941-9873-fabb9058e113.jpeg
Executed (default): INSERT INTO `logs` (`id`,`action`,`entityType`,`entityId`,`userId`,`username`,`ipAddress`,`deviceInfo`,`description`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?); Elapsed time: 3ms
收款码操作日志已记录: delete_qrcode
[0mDELETE /api/system/payment-qrcode [32m200[0m 10.526 ms - 53[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 2ms
[0mGET /api/notifications/unread-count [36m304[0m 6.501 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
开始处理支付收款码上传请求...
请求头: {"host":"localhost:3000","connection":"keep-alive","content-length":"294484","sec-ch-ua-platform":"\"macOS\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NTksInVzZXJuYW1lIjoi56em5pitIiwicm9sZSI6ImFkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzQ5MTE3ODE5LCJleHAiOjE3NDkxMjE0MTl9.MBvWXDvjfcfz7cChAMYgu7cyFc-XqwLvhsCbF_uldrs","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","content-type":"multipart/form-data; boundary=----WebKitFormBoundaryJIAjIVimxAEAV60C","sec-ch-ua-mobile":"?0","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"zh-CN,zh;q=0.9"}
支付码目录写入权限正常
收到文件: pay.png 类型: image/png
生成唯一文件名: qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png
文件上传成功: {
  fieldname: 'file',
  originalname: 'pay.png',
  encoding: '7bit',
  mimetype: 'image/png',
  destination: '/Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment',
  filename: 'qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png',
  path: '/Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment/qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png',
  size: 294304
}
文件大小: 294304 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png
尝试删除旧的收款码文件...
读取到的收款码配置: { filename: null, uploadTime: null }
配置中没有文件名，无需删除旧文件
删除旧收款码结果: false
已更新收款码配置: {
  filename: 'qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png',
  uploadTime: '2025-06-05T10:12:58.000Z'
}
Executed (default): INSERT INTO `logs` (`id`,`action`,`entityType`,`entityId`,`userId`,`username`,`ipAddress`,`deviceInfo`,`description`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?); Elapsed time: 1ms
收款码操作日志已记录: upload_qrcode
[0mPOST /api/system/payment-qrcode [32m200[0m 17.979 ms - 257[0m
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png',
  uploadTime: '2025-06-05T10:12:58.000Z'
}
检查收款码文件 qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png 是否存在: true
收款码文件大小: 294304 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png
[0mGET /api/system/payment-qrcode [32m200[0m 1.464 ms - 202[0m
[0mGET /uploads/payment/qrcode_df707ae2-fee3-4d4b-82ee-a012c5d625b7.png?t=1749118379017 [32m200[0m 1.247 ms - 294304[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 220 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.415 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 5.096 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 6.357 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 10.649 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 207
尝试验证令牌...
令牌验证成功, 用户ID: 220
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 1ms
认证成功, 用户: 测试 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 220 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 4.138 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 3.093 ms - -[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 220; Elapsed time: 3ms
用户认证信息(已更新): {
  id: 220,
  username: '测试',
  role: 'user',
  isAdmin: false,
  tokenRole: 'user',
  tokenIsAdmin: false
}
===== IP地址处理 =====
X-Forwarded-For: undefined
X-Real-IP: undefined
req.ip: ::1
connection.remoteAddress: ::1
原始IP地址: ::1
处理后的IP地址: 127.0.0.1
=====================
Executed (default): INSERT INTO `logs` (`id`,`action`,`entityType`,`entityId`,`userId`,`username`,`ipAddress`,`deviceInfo`,`description`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?); Elapsed time: 3ms
[0mPOST /api/auth/logout [32m200[0m 13.334 ms - 41[0m
[0mGET /api/health [32m200[0m 0.772 ms - 49[0m
飞书控制器: 请求获取登录URL
飞书服务: 生成授权URL, 应用凭证: {
  appId: 'cli_a66b3b2dcab8d013',
  redirectUri: 'http://localhost:3000/api/feishu/callback'
}
飞书服务: 完整授权URL: https://open.feishu.cn/open-apis/authen/v1/index?app_id=cli_a66b3b2dcab8d013&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fapi%2Ffeishu%2Fcallback&state=cpu3dh6shol
飞书控制器: 生成的授权URL: https://open.feishu.cn/open-apis/authen/v1/index?app_id=cli_a66b3b2dcab8d013&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fapi%2Ffeishu%2Fcallback&state=cpu3dh6shol
[0mGET /api/feishu/login-url [32m200[0m 1.884 ms - 235[0m
飞书回调: 收到授权码和状态: { code: '2FThI6c4dF88427I8DyeKLHG97400J0b', state: 'cpu3dh6shol' }
飞书回调: 获取访问令牌...
飞书服务: 正在用授权码获取访问令牌...
飞书服务: 请求数据: {"grant_type":"authorization_code","code":"2FThI6c4dF88427I8DyeKLHG97400J0b","app_id":"cli_a66b3b2dcab8d013","app_secret":"5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r"}
飞书服务: 访问令牌响应: {"code":0,"data":{"access_token":"u-di2PQj3Ld7JoW54Ud7_W9v0kn5J404wjXE0054Ay2J10","avatar_big":"https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=640x640&cut_type=&quality=&format=image&sticker_format=.webp","avatar_middle":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=240x240&cut_type=&quality=&format=image&sticker_format=.webp","avatar_thumb":"https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp","avatar_url":"https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp","email":"<EMAIL>","en_name":"杨永康","enterprise_email":"<EMAIL>","expires_in":6900,"mobile":"+8613396191271","name":"杨永康","open_id":"ou_020a00365e228028edd0c523a83b98d3","refresh_expires_in":2591700,"refresh_token":"ur-epL7m.QBd39X_gKOYNgAax0kn77404ajVo004kQy2E5g","sid":"AAAAAAAAAABmgV/oDoGAHA==","tenant_key":"2d25cdd5610f575e","token_type":"Bearer","union_id":"on_75f4af916fab74095c750a6ffb04d3a4","user_id":"g46e4688"},"msg":"success"}
飞书回调: 访问令牌获取成功
飞书回调: 获取用户信息...
飞书服务: 正在获取用户信息...
飞书服务: 用户信息响应状态码: 200
飞书服务: 用户信息完整响应: {
  "code": 0,
  "data": {
    "avatar_big": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=640x640&cut_type=&quality=&format=image&sticker_format=.webp",
    "avatar_middle": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=240x240&cut_type=&quality=&format=image&sticker_format=.webp",
    "avatar_thumb": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
    "avatar_url": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
    "email": "<EMAIL>",
    "employee_no": "YC81954",
    "en_name": "杨永康",
    "enterprise_email": "<EMAIL>",
    "mobile": "+8613396191271",
    "name": "杨永康",
    "open_id": "ou_020a00365e228028edd0c523a83b98d3",
    "tenant_key": "2d25cdd5610f575e",
    "union_id": "on_75f4af916fab74095c750a6ffb04d3a4",
    "user_id": "g46e4688"
  },
  "msg": "success"
}
飞书服务: 检查城市相关字段:
- mobile: +8613396191271
- mobile_visible: 未设置
- city: 未设置
飞书服务: ⚠️  用户信息API中没有城市信息
飞书服务: 使用open_id获取用户详细信息: ou_020a00365e228028edd0c523a83b98d3
飞书服务: 正在获取用户详细信息...
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 6.224 ms - -[0m
飞书服务: 用户详细信息响应: {"code":0,"data":{"user":{"mobile_visible":true,"open_id":"ou_020a00365e228028edd0c523a83b98d3","union_id":"on_75f4af916fab74095c750a6ffb04d3a4","user_id":"g46e4688"}},"msg":"success"}
飞书服务: 用户详细信息中的关键字段:
- 用户ID: g46e4688
- 姓名: undefined
- 企业邮箱: 未提供
- 个人邮箱: 未提供
- 城市/工作地点: 未提供
- 部门IDs: 未提供或为空
- 职务: 未提供
- 手机号: 未提供
- 手机号可见性: true
飞书服务: 警告 - 用户详细信息中没有手机号，这可能是因为:
1. 该用户在飞书中未设置手机号
2. 应用权限不足，需要申请contact:user.phone:readonly权限
3. 用户隐私设置不允许显示手机号
飞书服务: 用户详细信息完整数据: {
  "mobile_visible": true,
  "open_id": "ou_020a00365e228028edd0c523a83b98d3",
  "union_id": "on_75f4af916fab74095c750a6ffb04d3a4",
  "user_id": "g46e4688"
}
飞书服务: 警告 - 用户没有部门ID信息，这可能是因为:
1. 该用户在飞书中未被分配到任何部门
2. 应用权限不足，需要申请通讯录相关权限
飞书服务: 警告 - 用户没有企业邮箱，这可能是因为:
1. 该用户在飞书中未设置企业邮箱
2. 应用权限不足，需要申请mail:user_mailbox:readonly权限
飞书服务: 获取到用户详细信息，合并数据
飞书服务: 企业邮箱为空，将使用基本信息中的邮箱
飞书服务: 城市/工作地点为空
飞书服务: 合并后的用户数据: {
  "avatar_big": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=640x640&cut_type=&quality=&format=image&sticker_format=.webp",
  "avatar_middle": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=240x240&cut_type=&quality=&format=image&sticker_format=.webp",
  "avatar_thumb": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
  "avatar_url": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
  "email": "<EMAIL>",
  "employee_no": "YC81954",
  "en_name": "杨永康",
  "mobile": "+8613396191271",
  "name": "杨永康",
  "open_id": "ou_020a00365e228028edd0c523a83b98d3",
  "tenant_key": "2d25cdd5610f575e",
  "union_id": "on_75f4af916fab74095c750a6ffb04d3a4",
  "user_id": "g46e4688",
  "mobile_visible": true,
  "department_ids": []
}
飞书服务: 最终使用的邮箱: <EMAIL>
飞书服务: 最终使用的城市/工作地点: 未提供
飞书回调: 用户信息获取成功
飞书回调: 获取用户详细信息（包含部门ID）...
飞书服务: 正在获取用户详细信息...
飞书服务: 用户详细信息响应: {"code":0,"data":{"user":{"mobile_visible":true,"open_id":"ou_020a00365e228028edd0c523a83b98d3","union_id":"on_75f4af916fab74095c750a6ffb04d3a4","user_id":"g46e4688"}},"msg":"success"}
飞书服务: 用户详细信息中的关键字段:
- 用户ID: g46e4688
- 姓名: undefined
- 企业邮箱: 未提供
- 个人邮箱: 未提供
- 城市/工作地点: 未提供
- 部门IDs: 未提供或为空
- 职务: 未提供
- 手机号: 未提供
- 手机号可见性: true
飞书服务: 警告 - 用户详细信息中没有手机号，这可能是因为:
1. 该用户在飞书中未设置手机号
2. 应用权限不足，需要申请contact:user.phone:readonly权限
3. 用户隐私设置不允许显示手机号
飞书服务: 用户详细信息完整数据: {
  "mobile_visible": true,
  "open_id": "ou_020a00365e228028edd0c523a83b98d3",
  "union_id": "on_75f4af916fab74095c750a6ffb04d3a4",
  "user_id": "g46e4688"
}
飞书服务: 警告 - 用户没有部门ID信息，这可能是因为:
1. 该用户在飞书中未被分配到任何部门
2. 应用权限不足，需要申请通讯录相关权限
飞书服务: 警告 - 用户没有企业邮箱，这可能是因为:
1. 该用户在飞书中未设置企业邮箱
2. 应用权限不足，需要申请mail:user_mailbox:readonly权限
飞书回调: 用户详细信息获取成功
飞书回调: 最终用户信息:
- 用户名: 杨永康
- 邮箱: <EMAIL>
- 企业邮箱: 未提供
- 手机号: +8613396191271
- 城市: 未提供
- 部门ID: 未提供或为空
飞书回调: 创建或更新用户...
飞书服务: 正在创建或更新用户...
用户信息: {
  "avatar_big": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=640x640&cut_type=&quality=&format=image&sticker_format=.webp",
  "avatar_middle": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=240x240&cut_type=&quality=&format=image&sticker_format=.webp",
  "avatar_thumb": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
  "avatar_url": "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp",
  "email": "<EMAIL>",
  "employee_no": "YC81954",
  "en_name": "杨永康",
  "mobile": "+8613396191271",
  "name": "杨永康",
  "open_id": "ou_020a00365e228028edd0c523a83b98d3",
  "tenant_key": "2d25cdd5610f575e",
  "union_id": "on_75f4af916fab74095c750a6ffb04d3a4",
  "user_id": "g46e4688",
  "mobile_visible": true,
  "department_ids": []
}
飞书服务: 获取到登录IP: 127.0.0.1
飞书服务: 用户邮箱信息检查:
- 基础信息中的邮箱: <EMAIL>
- 企业邮箱: 未提供
- 手机号码: +8613396191271
飞书服务: 工作城市信息检查:
⚠️  用户信息中未找到工作城市字段
飞书服务: 将使用邮箱: <EMAIL>
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`feishuOpenId` = 'ou_020a00365e228028edd0c523a83b98d3' LIMIT 1; Elapsed time: 3ms
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`feishuUnionId` = 'on_75f4af916fab74095c750a6ffb04d3a4' LIMIT 1; Elapsed time: 1ms
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>'; Elapsed time: 1ms
飞书服务: 开始获取tenant_access_token...
飞书服务: 请求tenant_access_token的URL: https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal
飞书服务: 使用的应用ID: cli_a66b3b2dcab8d013
飞书服务: tenant_access_token响应状态码: 200
飞书服务: tenant_access_token响应: {"code":0,"expire":7200,"msg":"ok","tenant_access_token":"t-g10465idVJ6RW744LCOXLM3TMXUPBIV7XOKO2JEP"}
飞书服务: 成功获取tenant_access_token
飞书服务: 获取用户完整详细信息...
飞书服务: 用户详细信息: {
  "avatar": {
    "avatar_240": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp",
    "avatar_640": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp",
    "avatar_72": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp",
    "avatar_origin": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp"
  },
  "city": "武汉",
  "country": "CN",
  "department_ids": [
    "od-26b2f10f419d5507281703c79ba66a54"
  ],
  "description": "快速反馈：https://guanghe.feishu.cn/share/base/form/shrcnjIWlsuG4szZvsiP7cgHSWg​，高效处理",
  "email": "<EMAIL>",
  "employee_no": "YC81954",
  "employee_type": 1,
  "en_name": "",
  "enterprise_email": "<EMAIL>",
  "gender": 1,
  "is_tenant_manager": false,
  "job_title": "IT",
  "join_time": 1688947200,
  "leader_user_id": "ou_8e0e96ff4df78564d009da38c19f2a6d",
  "mobile": "+8613396191271",
  "mobile_visible": true,
  "name": "杨永康",
  "open_id": "ou_020a00365e228028edd0c523a83b98d3",
  "orders": [
    {
      "department_id": "od-26b2f10f419d5507281703c79ba66a54",
      "department_order": 1,
      "is_primary_dept": true,
      "user_order": 0
    }
  ],
  "status": {
    "is_activated": true,
    "is_exited": false,
    "is_frozen": false,
    "is_resigned": false,
    "is_unjoin": false
  },
  "union_id": "on_75f4af916fab74095c750a6ffb04d3a4",
  "user_id": "g46e4688",
  "work_station": ""
}
飞书服务: 获取用户部门信息，主要部门ID: od-26b2f10f419d5507281703c79ba66a54
飞书服务: 开始获取用户部门信息, 用户ID: ou_020a00365e228028edd0c523a83b98d3
飞书服务: 请求用户详情URL: https://open.feishu.cn/open-apis/contact/v3/users/ou_020a00365e228028edd0c523a83b98d3
飞书服务: 获取用户部门响应: {"code":0,"data":{"user":{"avatar":{"avatar_240":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp","avatar_640":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp","avatar_72":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp","avatar_origin":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00j6_4c5d4669-b331-4630-8e8b-ff30cca2f68g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp"},"city":"武汉","country":"CN","department_ids":["od-26b2f10f419d5507281703c79ba66a54"],"description":"快速反馈：https://guanghe.feishu.cn/share/base/form/shrcnjIWlsuG4szZvsiP7cgHSWg​，高效处理","email":"<EMAIL>","employee_no":"YC81954","employee_type":1,"en_name":"","enterprise_email":"<EMAIL>","gender":1,"is_tenant_manager":false,"job_title":"IT","join_time":1688947200,"leader_user_id":"ou_8e0e96ff4df78564d009da38c19f2a6d","mobile":"+8613396191271","mobile_visible":true,"name":"杨永康","open_id":"ou_020a00365e228028edd0c523a83b98d3","orders":[{"department_id":"od-26b2f10f419d5507281703c79ba66a54","department_order":1,"is_primary_dept":true,"user_order":0}],"status":{"is_activated":true,"is_exited":false,"is_frozen":false,"is_resigned":false,"is_unjoin":false},"union_id":"on_75f4af916fab74095c750a6ffb04d3a4","user_id":"g46e4688","work_station":""}},"msg":"success"}
飞书服务: 获取到用户部门ID列表: [ 'od-26b2f10f419d5507281703c79ba66a54' ]
飞书服务: 尝试获取部门名称, 部门ID: od-26b2f10f419d5507281703c79ba66a54
飞书服务: 成功获取部门名称: 武汉行政
飞书服务: 获取到部门名称: 武汉行政
飞书服务: 开始获取部门路径, 部门ID: od-26b2f10f419d5507281703c79ba66a54
飞书服务: 获取部门路径迭代 1/10, 当前部门ID: od-26b2f10f419d5507281703c79ba66a54
飞书服务: 尝试获取部门名称, 部门ID: od-26b2f10f419d5507281703c79ba66a54
飞书服务: 成功获取部门名称: 武汉行政
飞书服务: 当前部门名称: 武汉行政
飞书服务: 请求部门信息URL: https://open.feishu.cn/open-apis/contact/v3/departments/od-26b2f10f419d5507281703c79ba66a54
飞书服务: 获取部门路径迭代 2/9, 当前部门ID: od-d84a31d61917a78faf399dd91358090d
飞书服务: 尝试获取部门名称, 部门ID: od-d84a31d61917a78faf399dd91358090d
飞书服务: 成功获取部门名称: 行政团队
飞书服务: 当前部门名称: 行政团队
飞书服务: 请求部门信息URL: https://open.feishu.cn/open-apis/contact/v3/departments/od-d84a31d61917a78faf399dd91358090d
飞书服务: 已到达组织根部门，路径获取完成
飞书服务: 获取到完整部门路径: 行政团队/武汉行政
飞书服务: 获取到部门路径: 行政团队/武汉行政
飞书服务: 最终返回的部门信息: {"departmentId":"od-26b2f10f419d5507281703c79ba66a54","departmentName":"武汉行政","departmentPath":"行政团队/武汉行政"}
飞书服务: 获取到用户部门名称: 武汉行政
飞书服务: 获取到用户部门路径: 行政团队/武汉行政
飞书服务: 从详细信息中找到手机号码: +8613396191271
飞书服务: 从详细信息中找到职务: IT
✅ 通过企业访问令牌找到工作城市: 武汉
飞书服务: 开始职场获取流程...
飞书服务: 从飞书API获取到工作地点: 武汉
Executed (default): SELECT `id`, `name`, `code`, `description`, `isActive`, `createdAt`, `updatedAt` FROM `workplaces` AS `Workplace` WHERE `Workplace`.`name` = '武汉' AND `Workplace`.`isActive` = true; Elapsed time: 1ms
飞书服务: ✅ 直接匹配到职场: 武汉, ID: 2
飞书服务: 未找到现有用户，创建新用户...
Executed (default): INSERT INTO `users` (`id`,`username`,`email`,`password`,`role`,`department`,`departmentPath`,`mobile`,`workplace`,`workplaceId`,`feishuOpenId`,`feishuUnionId`,`feishuAvatar`,`feishuAccessToken`,`feishuRefreshToken`,`feishuTokenExpireTime`,`authType`,`points`,`lastLoginAt`,`lastLoginIp`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?); Elapsed time: 4ms
飞书服务: 用户创建成功, ID: 221
🎉 发送新用户欢迎通知: 杨永康
飞书机器人: 准备发送卡片消息...
卡片数据: {
  "msg_type": "interactive",
  "card": {
    "elements": [
      {
        "tag": "div",
        "text": {
          "content": "🎉 **新用户加入光年小卖部**\n\n👤 **用户信息**\n• 姓名：**杨永康** (行政团队/武汉行政)\n• 注册时间：2025/6/5 18:13:47\n• 认证方式：飞书登录\n**手机**: 13396191271\n**邮箱**: <EMAIL>\n\n🛍️ **欢迎加入**\n新成员已成功通过飞书登录系统，可以开始兑换商品啦！",
          "tag": "lark_md"
        }
      },
      {
        "tag": "action",
        "actions": [
          {
            "tag": "button",
            "text": {
              "content": "👥 查看用户管理",
              "tag": "plain_text"
            },
            "type": "primary",
            "url": "http://localhost:5173/admin/users",
            "value": {}
          }
        ]
      }
    ],
    "header": {
      "title": {
        "content": "🎊 光年小卖部 - 新用户欢迎",
        "tag": "plain_text"
      },
      "template": "green"
    }
  }
}
飞书机器人: 消息发送成功 {
  StatusCode: 0,
  StatusMessage: 'success',
  code: 0,
  data: {},
  msg: 'success'
}
✅ 新用户欢迎通知发送成功
飞书回调: 用户创建或更新成功
飞书回调: 用户ID: 221, 用户名: 杨永康, 部门: 武汉行政
飞书回调: 生成JWT令牌...
生成JWT令牌...
用户ID: 221
用户名: 杨永康
用户角色: user
记住我: true
JWT负载内容: {
  "id": 221,
  "username": "杨永康",
  "role": "user",
  "isAdmin": false
}
JWT令牌生成成功，过期时间: 30d
飞书回调: JWT令牌生成成功
===== IP地址处理 =====
X-Forwarded-For: undefined
X-Real-IP: undefined
req.ip: ::1
connection.remoteAddress: ::1
原始IP地址: ::1
处理后的IP地址: 127.0.0.1
=====================
Executed (default): INSERT INTO `logs` (`id`,`action`,`entityType`,`entityId`,`userId`,`username`,`ipAddress`,`deviceInfo`,`description`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?); Elapsed time: 1ms
[0mGET /api/feishu/callback?code=2FThI6c4dF88427I8DyeKLHG97400J0b&state=cpu3dh6shol [32m200[0m 3435.061 ms - -[0m
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT `id`, `name`, `description`, `sortOrder`, `createdAt`, `updatedAt` FROM `categories` AS `Category` ORDER BY `Category`.`sortOrder` ASC, `Category`.`name` ASC; Elapsed time: 8ms
[0mGET /api/categories [36m304[0m 9.675 ms - -[0m
Executed (default): SELECT count(DISTINCT(`Product`.`id`)) AS `count` FROM `products` AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` WHERE `Product`.`status` = 'active'; Elapsed time: 6ms
Executed (default): SELECT `Product`.*, `Category`.`id` AS `Category.id`, `Category`.`name` AS `Category.name`, `ProductImages`.`id` AS `ProductImages.id`, `ProductImages`.`imageUrl` AS `ProductImages.imageUrl`, `ProductImages`.`sortOrder` AS `ProductImages.sortOrder` FROM (SELECT `Product`.`id`, `Product`.`name`, `Product`.`categoryId`, `Product`.`lyPrice`, `Product`.`rmbPrice`, `Product`.`description`, `Product`.`stock`, `Product`.`exchangeCount`, `Product`.`isHot`, `Product`.`isNew`, `Product`.`status`, `Product`.`createdAt`, `Product`.`updatedAt` FROM `products` AS `Product` WHERE `Product`.`status` = 'active' ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC LIMIT 0, 50) AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC; Elapsed time: 5ms
[0mGET /api/products?page=1&limit=50&showAll=false [36m304[0m 15.606 ms - -[0m
Executed (default): SELECT count(`Announcement`.`id`) AS `count` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active'; Elapsed time: 7ms
Executed (default): SELECT `Announcement`.`id`, `Announcement`.`title`, `Announcement`.`content`, `Announcement`.`contentHtml`, `Announcement`.`type`, `Announcement`.`status`, `Announcement`.`imageUrl`, `Announcement`.`imageUrls`, `Announcement`.`createdBy`, `Announcement`.`createdAt`, `Announcement`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`username` AS `creator.username` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active' ORDER BY `Announcement`.`createdAt` DESC LIMIT 0, 1; Elapsed time: 3ms
[0mGET /api/announcements?page=1&limit=1&sort=newest&status=active [36m304[0m 13.190 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 211
尝试验证令牌...
令牌验证成功, 用户ID: 221
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 2ms
认证成功, 用户: 杨永康 角色: user
Executed (default): SELECT MIN(`lyPrice`) AS `minLyPrice`, MAX(`lyPrice`) AS `maxLyPrice`, MIN(`rmbPrice`) AS `minRmbPrice`, MAX(`rmbPrice`) AS `maxRmbPrice` FROM `products` AS `Product` WHERE `Product`.`status` = 'active'; Elapsed time: 2ms
[0mGET /api/products/price-ranges [36m304[0m 2.455 ms - -[0m
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 221 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [32m200[0m 5.872 ms - 35[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 1ms
用户认证信息(已更新): {
  id: 221,
  username: '杨永康',
  role: 'user',
  isAdmin: false,
  tokenRole: 'user',
  tokenIsAdmin: false
}
查询条件: {"userId":221}
用户ID: 221
Executed (default): SELECT `Exchange`.`id`, `Exchange`.`orderNumber`, `Exchange`.`userId`, `Exchange`.`productId`, `Exchange`.`quantity`, `Exchange`.`totalAmount`, `Exchange`.`paymentMethod`, `Exchange`.`contactInfo`, `Exchange`.`location`, `Exchange`.`workplaceId`, `Exchange`.`remarks`, `Exchange`.`paymentProofUrl`, `Exchange`.`status`, `Exchange`.`adminRemarks`, `Exchange`.`trackingNumber`, `Exchange`.`trackingCompany`, `Exchange`.`createdAt`, `Exchange`.`updatedAt`, `Product`.`id` AS `Product.id`, `Product`.`name` AS `Product.name`, `Product`.`lyPrice` AS `Product.lyPrice`, `Product`.`rmbPrice` AS `Product.rmbPrice` FROM `exchanges` AS `Exchange` LEFT OUTER JOIN `products` AS `Product` ON `Exchange`.`productId` = `Product`.`id` WHERE `Exchange`.`userId` = 221 ORDER BY `Exchange`.`createdAt` DESC LIMIT 0, 10; Elapsed time: 3ms
Executed (default): SELECT count(`Exchange`.`id`) AS `count` FROM `exchanges` AS `Exchange` LEFT OUTER JOIN `products` AS `Product` ON `Exchange`.`productId` = `Product`.`id` WHERE `Exchange`.`userId` = 221; Elapsed time: 3ms
查询结果数量: 0
[0mGET /api/exchanges/user?page=1&limit=10&status= [32m200[0m 9.697 ms - 45[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 1ms
用户认证信息(已更新): {
  id: 221,
  username: '杨永康',
  role: 'user',
  isAdmin: false,
  tokenRole: 'user',
  tokenIsAdmin: false
}
Executed (default): SELECT `Feedback`.`id`, `Feedback`.`title`, `Feedback`.`content`, `Feedback`.`type`, `Feedback`.`status`, `Feedback`.`userId`, `Feedback`.`adminReply`, `Feedback`.`createdAt`, `Feedback`.`updatedAt`, `user`.`id` AS `user.id`, `user`.`username` AS `user.username` FROM `feedbacks` AS `Feedback` LEFT OUTER JOIN `users` AS `user` ON `Feedback`.`userId` = `user`.`id` WHERE `Feedback`.`userId` = 221 ORDER BY `Feedback`.`createdAt` DESC LIMIT 0, 10; Elapsed time: 3ms
Executed (default): SELECT count(`Feedback`.`id`) AS `count` FROM `feedbacks` AS `Feedback` LEFT OUTER JOIN `users` AS `user` ON `Feedback`.`userId` = `user`.`id` WHERE `Feedback`.`userId` = 221; Elapsed time: 3ms
[0mGET /api/feedback/user?page=1&limit=10&status= [32m200[0m 10.354 ms - 45[0m
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 1ms
用户认证信息(已更新): {
  id: 221,
  username: '杨永康',
  role: 'user',
  isAdmin: false,
  tokenRole: 'user',
  tokenIsAdmin: false
}
查询条件: {"userId":221}
用户ID: 221
Executed (default): SELECT count(`Exchange`.`id`) AS `count` FROM `exchanges` AS `Exchange` LEFT OUTER JOIN `products` AS `Product` ON `Exchange`.`productId` = `Product`.`id` WHERE `Exchange`.`userId` = 221; Elapsed time: 1ms
Executed (default): SELECT `Exchange`.`id`, `Exchange`.`orderNumber`, `Exchange`.`userId`, `Exchange`.`productId`, `Exchange`.`quantity`, `Exchange`.`totalAmount`, `Exchange`.`paymentMethod`, `Exchange`.`contactInfo`, `Exchange`.`location`, `Exchange`.`workplaceId`, `Exchange`.`remarks`, `Exchange`.`paymentProofUrl`, `Exchange`.`status`, `Exchange`.`adminRemarks`, `Exchange`.`trackingNumber`, `Exchange`.`trackingCompany`, `Exchange`.`createdAt`, `Exchange`.`updatedAt`, `Product`.`id` AS `Product.id`, `Product`.`name` AS `Product.name`, `Product`.`lyPrice` AS `Product.lyPrice`, `Product`.`rmbPrice` AS `Product.rmbPrice` FROM `exchanges` AS `Exchange` LEFT OUTER JOIN `products` AS `Product` ON `Exchange`.`productId` = `Product`.`id` WHERE `Exchange`.`userId` = 221 ORDER BY `Exchange`.`createdAt` DESC LIMIT 0, 10; Elapsed time: 1ms
查询结果数量: 0
[0mGET /api/exchanges/user?page=1&limit=10&status= [36m304[0m 6.436 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.070 ms - -[0m
Executed (default): SELECT `id`, `name`, `description`, `sortOrder`, `createdAt`, `updatedAt` FROM `categories` AS `Category` ORDER BY `Category`.`sortOrder` ASC, `Category`.`name` ASC; Elapsed time: 6ms
[0mGET /api/categories [36m304[0m 7.728 ms - -[0m
Executed (default): SELECT count(DISTINCT(`Product`.`id`)) AS `count` FROM `products` AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` WHERE `Product`.`status` = 'active'; Elapsed time: 5ms
Executed (default): SELECT `Product`.*, `Category`.`id` AS `Category.id`, `Category`.`name` AS `Category.name`, `ProductImages`.`id` AS `ProductImages.id`, `ProductImages`.`imageUrl` AS `ProductImages.imageUrl`, `ProductImages`.`sortOrder` AS `ProductImages.sortOrder` FROM (SELECT `Product`.`id`, `Product`.`name`, `Product`.`categoryId`, `Product`.`lyPrice`, `Product`.`rmbPrice`, `Product`.`description`, `Product`.`stock`, `Product`.`exchangeCount`, `Product`.`isHot`, `Product`.`isNew`, `Product`.`status`, `Product`.`createdAt`, `Product`.`updatedAt` FROM `products` AS `Product` WHERE `Product`.`status` = 'active' ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC LIMIT 0, 50) AS `Product` LEFT OUTER JOIN `categories` AS `Category` ON `Product`.`categoryId` = `Category`.`id` LEFT OUTER JOIN `product_images` AS `ProductImages` ON `Product`.`id` = `ProductImages`.`productId` ORDER BY `Product`.`isNew` DESC, `Product`.`isHot` DESC, `Product`.`lyPrice` ASC; Elapsed time: 5ms
[0mGET /api/products?page=1&limit=50&showAll=false [36m304[0m 11.145 ms - -[0m
Executed (default): SELECT count(`Announcement`.`id`) AS `count` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active'; Elapsed time: 7ms
Executed (default): SELECT `Announcement`.`id`, `Announcement`.`title`, `Announcement`.`content`, `Announcement`.`contentHtml`, `Announcement`.`type`, `Announcement`.`status`, `Announcement`.`imageUrl`, `Announcement`.`imageUrls`, `Announcement`.`createdBy`, `Announcement`.`createdAt`, `Announcement`.`updatedAt`, `creator`.`id` AS `creator.id`, `creator`.`username` AS `creator.username` FROM `announcements` AS `Announcement` LEFT OUTER JOIN `users` AS `creator` ON `Announcement`.`createdBy` = `creator`.`id` WHERE `Announcement`.`status` = 'active' ORDER BY `Announcement`.`createdAt` DESC LIMIT 0, 1; Elapsed time: 6ms
[0mGET /api/announcements?page=1&limit=1&sort=newest&status=active [36m304[0m 8.884 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 211
尝试验证令牌...
令牌验证成功, 用户ID: 221
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 1ms
认证成功, 用户: 杨永康 角色: user
Executed (default): SELECT MIN(`lyPrice`) AS `minLyPrice`, MAX(`lyPrice`) AS `maxLyPrice`, MIN(`rmbPrice`) AS `minRmbPrice`, MAX(`rmbPrice`) AS `maxRmbPrice` FROM `products` AS `Product` WHERE `Product`.`status` = 'active'; Elapsed time: 1ms
[0mGET /api/products/price-ranges [36m304[0m 2.143 ms - -[0m
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 221 AND `Notification`.`isRead` = false; Elapsed time: 2ms
[0mGET /api/notifications/unread-count [36m304[0m 8.876 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 5.913 ms - -[0m
[0mGET /uploads/c6f6521cfd30b8551ac7c45c730859e8.png [36m304[0m 0.721 ms - -[0m
[0mGET /uploads/f7b621699109d019b255f73f757e187f.png [36m304[0m 0.625 ms - -[0m
[0mGET /uploads/bfb7be5e9e3bcd5e941c2c6cc8f5b6be.png [32m200[0m 5.800 ms - 60982[0m
[0mGET /uploads/8c3c43fcd78445e53fe7a670de4fc544.png [36m304[0m 1.909 ms - -[0m
[0mGET /uploads/34de1888c250bfaa6135298e78e0c45d.png [36m304[0m 2.626 ms - -[0m
[0mGET /uploads/39e227e4ed19adba0b7112db94ed5f45.png [36m304[0m 1.459 ms - -[0m
[0mGET /uploads/a3d2662d0d4e6b4730b4b923093f64be.png [36m304[0m 2.700 ms - -[0m
[0mGET /uploads/722240fa77edc682ce096927145ec734.png [36m304[0m 1.521 ms - -[0m
[0mGET /uploads/ae8d126d025db7e09e5ee6d68ba68914.png [36m304[0m 1.344 ms - -[0m
[0mGET /uploads/52c2de7251fc9bf8cceef5a078162d0e.png [32m200[0m 4.973 ms - 216811[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 4ms
[0mGET /api/notifications/unread-count [36m304[0m 8.938 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 4.918 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 211
尝试验证令牌...
令牌验证成功, 用户ID: 221
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 1ms
认证成功, 用户: 杨永康 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 221 AND `Notification`.`isRead` = false; Elapsed time: 24ms
[0mGET /api/notifications/unread-count [36m304[0m 30.919 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 8.916 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 0ms
[0mGET /api/notifications/unread-count [36m304[0m 5.430 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 11.418 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 211
尝试验证令牌...
令牌验证成功, 用户ID: 221
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 2ms
认证成功, 用户: 杨永康 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 221 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 5.163 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 1ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 7.231 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 2ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 8.462 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 205
尝试验证令牌...
令牌验证成功, 用户ID: 59
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 59; Elapsed time: 3ms
认证成功, 用户: 秦昭 角色: admin
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 59 AND `Notification`.`isRead` = false; Elapsed time: 2ms
[0mGET /api/notifications/unread-count [36m304[0m 9.731 ms - -[0m
开始认证流程...
收到认证令牌, 长度: 211
尝试验证令牌...
令牌验证成功, 用户ID: 221
查询用户数据...
Executed (default): SELECT `id`, `username`, `email`, `password`, `role`, `department`, `departmentPath`, `mobile`, `workplace`, `workplaceId`, `feishuOpenId`, `feishuUnionId`, `feishuUserId`, `feishuAvatar`, `feishuAccessToken`, `feishuRefreshToken`, `feishuTokenExpireTime`, `authType`, `points`, `lastLoginAt`, `lastLoginIp`, `createdAt`, `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 221; Elapsed time: 1ms
认证成功, 用户: 杨永康 角色: user
Executed (default): SELECT count(*) AS `count` FROM `notifications` AS `Notification` WHERE `Notification`.`recipientId` = 221 AND `Notification`.`isRead` = false; Elapsed time: 1ms
[0mGET /api/notifications/unread-count [36m304[0m 6.010 ms - -[0m
